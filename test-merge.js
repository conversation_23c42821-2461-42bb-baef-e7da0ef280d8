const fs = require('fs');
const path = require('path');

const INPUT_DIR = './data';
const OUTPUT_FILE = './merged.csv';

const HEADERS = [
  'timestamp',
  'candle_open','candle_high','candle_low','candle_close','candle_quoteVol','candle_baseVol',
  'market_indexPrice','market_marketPrice','market_fundingRate',
  'trades_buyVolume','trades_sellVolume',
  'depth_bestBidPrice','depth_bestBidSize','depth_bestAskPrice','depth_bestAskSize'
];

function safeReadJSON(filePath) {
  const data = [];
  if (!fs.existsSync(filePath)) return data;
  const lines = fs.readFileSync(filePath, 'utf-8').split(/\r?\n/);
  for (const line of lines) {
    if (!line.trim()) continue;
    try {
      data.push(JSON.parse(line));
    } catch {
      console.warn(`Skipping invalid JSON line in ${filePath}`);
    }
  }
  return data;
}

function normalizeTime(ts) {
  if (!ts) return 0;
  let timeInMs;

  if (typeof ts === 'number') {
    timeInMs = ts;
  } else {
    timeInMs = new Date(ts).getTime();
    if (isNaN(timeInMs)) {
      console.warn('Invalid timestamp:', ts);
      return 0;
    }
  }

  const result = Math.round(timeInMs / 1000);
  if (isNaN(result)) {
    console.warn('NaN result for timestamp:', ts, 'timeInMs:', timeInMs);
    return 0;
  }
  return result;
}

function normalizeCandles(arr) {
  const candlesByTimestamp = {};
  
  for (const c of arr) {
    const roundedTs = normalizeTime(c.time);
    if (roundedTs === 0) continue;
    if (!candlesByTimestamp[roundedTs] || c.timestamp > candlesByTimestamp[roundedTs].timestamp) {
      candlesByTimestamp[roundedTs] = c;
    }
  }
  
  return Object.entries(candlesByTimestamp).map(([timestamp, c]) => ({
    timestamp: +timestamp,
    candle_open: +c.open,
    candle_high: +c.high,
    candle_low: +c.low,
    candle_close: +c.close,
    candle_quoteVol: +c.quoteVol,
    candle_baseVol: +c.baseVol,
    market_indexPrice: '',
    market_marketPrice: '',
    market_fundingRate: '',
    trades_buyVolume: 0,
    trades_sellVolume: 0,
    depth_bestBidPrice: '',
    depth_bestBidSize: '',
    depth_bestAskPrice: '',
    depth_bestAskSize: ''
  }));
}

// Test with just one file first
const merged = {};
const candleData = normalizeCandles(safeReadJSON(path.join(INPUT_DIR, 'ETHUSDT_1m.json')));
console.log('Sample candle data:', candleData.slice(0, 3));
console.log('Total candle entries:', candleData.length);

// Check for any NaN timestamps
const nanEntries = candleData.filter(row => isNaN(row.timestamp));
if (nanEntries.length > 0) {
  console.log('Found NaN timestamps:', nanEntries);
}
