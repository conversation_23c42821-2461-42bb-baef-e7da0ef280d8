"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const INPUT_DIR = 'C:/Users/<USER>/ethusdt-merge/data';
const OUTPUT_FILE = './merged.csv';
const HEADERS = [
    'timestamp',
    'candle_open', 'candle_high', 'candle_low', 'candle_close', 'candle_quoteVol', 'candle_baseVol',
    'market_indexPrice', 'market_marketPrice', 'market_fundingRate',
    'trades_buyVolume', 'trades_sellVolume',
    'depth_bestBidPrice', 'depth_bestBidSize', 'depth_bestAskPrice', 'depth_bestAskSize'
];
function safeReadJSON(filePath) {
    const data = [];
    if (!fs_1.default.existsSync(filePath))
        return data;
    const lines = fs_1.default.readFileSync(filePath, 'utf-8').split(/\r?\n/);
    for (const line of lines) {
        if (!line.trim())
            continue;
        try {
            data.push(JSON.parse(line));
        }
        catch {
            console.warn(`Skipping invalid JSON line in ${filePath}`);
        }
    }
    return data;
}
function normalizeCandles(arr) {
    const candlesByTimestamp = {};
    for (const c of arr) {
        const roundedTs = normalizeTime(c.time);
        if (roundedTs === 0)
            continue;
        if (!candlesByTimestamp[roundedTs] || c.timestamp > candlesByTimestamp[roundedTs].timestamp) {
            candlesByTimestamp[roundedTs] = c;
        }
    }
    return Object.entries(candlesByTimestamp).map(([timestamp, c]) => ({
        timestamp: +timestamp,
        candle_open: +c.open,
        candle_high: +c.high,
        candle_low: +c.low,
        candle_close: +c.close,
        candle_quoteVol: +c.quoteVol,
        candle_baseVol: +c.baseVol,
        market_indexPrice: '',
        market_marketPrice: '',
        market_fundingRate: '',
        trades_buyVolume: 0,
        trades_sellVolume: 0,
        depth_bestBidPrice: '',
        depth_bestBidSize: '',
        depth_bestAskPrice: '',
        depth_bestAskSize: ''
    }));
}
function normalizeMarket(arr) {
    const marketByTimestamp = {};
    for (const m of arr) {
        const roundedTs = normalizeTime(m.timestamp);
        if (roundedTs === 0)
            continue;
        if (!marketByTimestamp[roundedTs] || m.timestamp > marketByTimestamp[roundedTs].timestamp) {
            marketByTimestamp[roundedTs] = m;
        }
    }
    return Object.entries(marketByTimestamp).map(([timestamp, m]) => ({
        timestamp: +timestamp,
        candle_open: '',
        candle_high: '',
        candle_low: '',
        candle_close: '',
        candle_quoteVol: '',
        candle_baseVol: '',
        market_indexPrice: +m.ip,
        market_marketPrice: +m.mp,
        market_fundingRate: +m.fr,
        trades_buyVolume: 0,
        trades_sellVolume: 0,
        depth_bestBidPrice: '',
        depth_bestBidSize: '',
        depth_bestAskPrice: '',
        depth_bestAskSize: ''
    }));
}
function normalizeTrades(arr) {
    const tradesByTimestamp = {};
    for (const entry of arr) {
        if (entry.trades && Array.isArray(entry.trades)) {
            for (const t of entry.trades) {
                const roundedTs = normalizeTime(t.t);
                if (roundedTs === 0)
                    continue;
                if (!tradesByTimestamp[roundedTs]) {
                    tradesByTimestamp[roundedTs] = { buyVolume: 0, sellVolume: 0 };
                }
                if (t.s === 'buy') {
                    tradesByTimestamp[roundedTs].buyVolume += +t.v;
                }
                else if (t.s === 'sell') {
                    tradesByTimestamp[roundedTs].sellVolume += +t.v;
                }
            }
        }
    }
    const rows = [];
    for (const [timestamp, volumes] of Object.entries(tradesByTimestamp)) {
        rows.push({
            timestamp: +timestamp,
            candle_open: '',
            candle_high: '',
            candle_low: '',
            candle_close: '',
            candle_quoteVol: '',
            candle_baseVol: '',
            market_indexPrice: '',
            market_marketPrice: '',
            market_fundingRate: '',
            trades_buyVolume: volumes.buyVolume,
            trades_sellVolume: volumes.sellVolume,
            depth_bestBidPrice: '',
            depth_bestBidSize: '',
            depth_bestAskPrice: '',
            depth_bestAskSize: ''
        });
    }
    return rows;
}
function normalizeDepth(arr) {
    const depthByTimestamp = {};
    for (const d of arr) {
        const roundedTs = normalizeTime(d.timestamp);
        if (roundedTs === 0)
            continue;
        if (!depthByTimestamp[roundedTs] || d.timestamp > depthByTimestamp[roundedTs].timestamp) {
            depthByTimestamp[roundedTs] = d;
        }
    }
    return Object.entries(depthByTimestamp).map(([timestamp, d]) => {
        const bestBid = d.bids && d.bids.length ? [+d.bids[0][0], +d.bids[0][1]] : [0, 0];
        const bestAsk = d.asks && d.asks.length ? [+d.asks[0][0], +d.asks[0][1]] : [0, 0];
        return {
            timestamp: +timestamp,
            candle_open: '',
            candle_high: '',
            candle_low: '',
            candle_close: '',
            candle_quoteVol: '',
            candle_baseVol: '',
            market_indexPrice: '',
            market_marketPrice: '',
            market_fundingRate: '',
            trades_buyVolume: 0,
            trades_sellVolume: 0,
            depth_bestBidPrice: bestBid[0],
            depth_bestBidSize: bestBid[1],
            depth_bestAskPrice: bestAsk[0],
            depth_bestAskSize: bestAsk[1]
        };
    });
}
function normalizeTime(ts) {
    if (!ts)
        return 0;
    let timeInMs;
    if (typeof ts === 'number') {
        timeInMs = ts;
    }
    else {
        timeInMs = new Date(ts).getTime();
        if (isNaN(timeInMs))
            return 0;
    }
    return Math.round(timeInMs / 1000);
}
function consolidateRows(rows, merged) {
    for (const row of rows) {
        const key = row.timestamp.toString();
        if (!merged[key]) {
            merged[key] = { ...emptyRow(row.timestamp), ...row };
        }
        else {
            for (const field of HEADERS) {
                if (field === 'timestamp')
                    continue;
                const val = row[field];
                if (field.startsWith('trades_') && typeof val === 'number' && !isNaN(val)) {
                    merged[key][field] = Math.round(((merged[key][field] || 0) + val) * 1000000) / 1000000;
                }
                else if (field.startsWith('candle_') && typeof val === 'number' && !isNaN(val)) {
                    if (val !== 0) {
                        merged[key][field] = val;
                    }
                }
                else if ((field.startsWith('market_') || field.startsWith('depth_')) && val !== '' && val !== 0) {
                    merged[key][field] = val;
                }
                else if (val !== '' && merged[key][field] === '') {
                    merged[key][field] = val;
                }
            }
        }
    }
}
function emptyRow(ts) {
    return {
        timestamp: ts,
        candle_open: '',
        candle_high: '',
        candle_low: '',
        candle_close: '',
        candle_quoteVol: '',
        candle_baseVol: '',
        market_indexPrice: '',
        market_marketPrice: '',
        market_fundingRate: '',
        trades_buyVolume: 0,
        trades_sellVolume: 0,
        depth_bestBidPrice: '',
        depth_bestBidSize: '',
        depth_bestAskPrice: '',
        depth_bestAskSize: ''
    };
}
function writeCSV(merged) {
    const rows = Object.values(merged)
        .sort((a, b) => a.timestamp - b.timestamp)
        .map(r => HEADERS.map(h => r[h] ?? '').join(','));
    fs_1.default.writeFileSync(OUTPUT_FILE, [HEADERS.join(','), ...rows].join('\n'));
    console.log(`Merged CSV written to: ${OUTPUT_FILE}`);
}
function findCandlePeriod(timestamp, candlePeriods) {
    // Find which candle period this timestamp falls into
    // Assuming 1-minute candles (60 seconds)
    for (const period of candlePeriods) {
        if (timestamp >= period && timestamp < period + 60) {
            return period;
        }
    }
    return null;
}
const merged = {};
// First, get all candle periods
const allCandleData = [];
['ETHUSDT_1m.json', 'ETHUSDT_3m.json', 'ETHUSDT_5m.json', 'ETHUSDT_15m.json'].forEach(file => {
    allCandleData.push(...safeReadJSON(path_1.default.join(INPUT_DIR, file)));
});
const candlePeriods = allCandleData.map(c => normalizeTime(c.time)).filter(t => t > 0).sort((a, b) => a - b);
console.log(`Found ${candlePeriods.length} candle periods`);
// Add candle data using their original timestamps
consolidateRows(normalizeCandles(allCandleData), merged);
// Group real-time data into candle periods
const realTimeData = [
    ...normalizeTrades(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Trades.json'))),
    ...normalizeMarket(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Market.json'))),
    ...normalizeDepth(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Depth.json')))
];
for (const row of realTimeData) {
    const candlePeriod = findCandlePeriod(row.timestamp, candlePeriods);
    if (candlePeriod) {
        row.timestamp = candlePeriod; // Group into candle period
        const key = candlePeriod.toString();
        if (!merged[key]) {
            merged[key] = { ...emptyRow(candlePeriod) };
        }
        // Consolidate the real-time data into the candle period
        for (const field of HEADERS) {
            if (field === 'timestamp')
                continue;
            const val = row[field];
            if (field.startsWith('trades_') && typeof val === 'number' && !isNaN(val)) {
                merged[key][field] = Math.round(((merged[key][field] || 0) + val) * 1000000) / 1000000;
            }
            else if ((field.startsWith('market_') || field.startsWith('depth_')) && val !== '' && val !== 0) {
                merged[key][field] = val;
            }
            else if (val !== '' && merged[key][field] === '') {
                merged[key][field] = val;
            }
        }
    }
}
writeCSV(merged);
