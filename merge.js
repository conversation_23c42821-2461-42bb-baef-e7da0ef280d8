"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const INPUT_DIR = 'C:/Users/<USER>/ethusdt-merge/data';
const OUTPUT_FILE = 'C:/Users/<USER>/output/merged.csv';
const HEADERS = [
    'timestamp',
    'candle_open', 'candle_high', 'candle_low', 'candle_close', 'candle_quoteVol', 'candle_baseVol',
    'market_indexPrice', 'market_marketPrice', 'market_fundingRate',
    'trades_buyVolume', 'trades_sellVolume',
    'depth_bestBidPrice', 'depth_bestBidSize', 'depth_bestAskPrice', 'depth_bestAskSize'
];
function safeReadJSON(filePath) {
    const data = [];
    if (!fs_1.default.existsSync(filePath))
        return data;
    const lines = fs_1.default.readFileSync(filePath, 'utf-8').split(/\r?\n/);
    for (const line of lines) {
        if (!line.trim())
            continue;
        try {
            data.push(JSON.parse(line));
        }
        catch {
            console.warn(`Skipping invalid JSON line in ${filePath}`);
        }
    }
    return data;
}
function normalizeCandles(arr) {
    return arr.map(c => ({
        timestamp: normalizeTime(c.time), // use 'time' for candle open time
        candle_open: +c.open,
        candle_high: +c.high,
        candle_low: +c.low,
        candle_close: +c.close,
        candle_quoteVol: +c.quoteVol,
        candle_baseVol: +c.baseVol,
        market_indexPrice: '',
        market_marketPrice: '',
        market_fundingRate: '',
        trades_buyVolume: 0,
        trades_sellVolume: 0,
        depth_bestBidPrice: '',
        depth_bestBidSize: '',
        depth_bestAskPrice: '',
        depth_bestAskSize: ''
    }));
}
function normalizeMarket(arr) {
    return arr.map(m => ({
        timestamp: normalizeTime(m.timestamp),
        candle_open: '',
        candle_high: '',
        candle_low: '',
        candle_close: '',
        candle_quoteVol: '',
        candle_baseVol: '',
        market_indexPrice: +m.ip,
        market_marketPrice: +m.mp,
        market_fundingRate: +m.fr,
        trades_buyVolume: 0,
        trades_sellVolume: 0,
        depth_bestBidPrice: '',
        depth_bestBidSize: '',
        depth_bestAskPrice: '',
        depth_bestAskSize: ''
    }));
}
function normalizeTrades(arr) {
    const rows = [];
    for (const entry of arr) {
        if (entry.trades && Array.isArray(entry.trades)) {
            for (const t of entry.trades) {
                rows.push({
                    timestamp: normalizeTime(t.t),
                    candle_open: '',
                    candle_high: '',
                    candle_low: '',
                    candle_close: '',
                    candle_quoteVol: '',
                    candle_baseVol: '',
                    market_indexPrice: '',
                    market_marketPrice: '',
                    market_fundingRate: '',
                    trades_buyVolume: t.s === 'buy' ? +t.v : 0,
                    trades_sellVolume: t.s === 'sell' ? +t.v : 0,
                    depth_bestBidPrice: '',
                    depth_bestBidSize: '',
                    depth_bestAskPrice: '',
                    depth_bestAskSize: ''
                });
            }
        }
    }
    return rows;
}
function normalizeDepth(arr) {
    return arr.map(d => {
        const bestBid = d.bids && d.bids.length ? [+d.bids[0][0], +d.bids[0][1]] : [0, 0];
        const bestAsk = d.asks && d.asks.length ? [+d.asks[0][0], +d.asks[0][1]] : [0, 0];
        return {
            timestamp: normalizeTime(d.timestamp),
            candle_open: '',
            candle_high: '',
            candle_low: '',
            candle_close: '',
            candle_quoteVol: '',
            candle_baseVol: '',
            market_indexPrice: '',
            market_marketPrice: '',
            market_fundingRate: '',
            trades_buyVolume: 0,
            trades_sellVolume: 0,
            depth_bestBidPrice: bestBid[0],
            depth_bestBidSize: bestBid[1],
            depth_bestAskPrice: bestAsk[0],
            depth_bestAskSize: bestAsk[1]
        };
    });
}
function normalizeTime(ts) {
    if (!ts)
        return 0;
    if (typeof ts === 'number')
        return Math.floor(ts / 1000);
    // ISO string
    return Math.floor(new Date(ts).getTime() / 1000);
}
function consolidateRows(rows, merged) {
    for (const row of rows) {
        const targetTs = findNearestTimestamp(merged, row.timestamp);
        const key = targetTs ?? row.timestamp.toString();
        if (!merged[key]) {
            merged[key] = { ...emptyRow(row.timestamp), ...row };
        }
        else {
            for (const field of HEADERS) {
                if (field === 'timestamp')
                    continue;
                const val = row[field];
                if (typeof val === 'number' && !isNaN(val)) {
                    merged[key][field] = (merged[key][field] || 0) + val;
                }
                else if (val !== '' && merged[key][field] === '') {
                    merged[key][field] = val;
                }
            }
        }
    }
}
function findNearestTimestamp(merged, ts) {
    const tolerance = 1; // ±1 second
    for (const key in merged) {
        if (Math.abs(parseInt(key, 10) - ts) <= tolerance)
            return key;
    }
    return null;
}
function emptyRow(ts) {
    const obj = {};
    for (const h of HEADERS)
        obj[h] = '';
    obj.timestamp = ts;
    return obj;
}
function writeCSV(merged) {
    const rows = Object.values(merged)
        .sort((a, b) => a.timestamp - b.timestamp)
        .map(r => HEADERS.map(h => r[h] ?? '').join(','));
    fs_1.default.writeFileSync(OUTPUT_FILE, [HEADERS.join(','), ...rows].join('\n'));
    console.log(`Merged CSV written to: ${OUTPUT_FILE}`);
}
// ---- MAIN ----
const merged = {};
['ETHUSDT_1m.json', 'ETHUSDT_3m.json', 'ETHUSDT_5m.json', 'ETHUSDT_15m.json'].forEach(file => {
    consolidateRows(normalizeCandles(safeReadJSON(path_1.default.join(INPUT_DIR, file))), merged);
});
consolidateRows(normalizeTrades(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Trades.json'))), merged);
consolidateRows(normalizeMarket(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Market.json'))), merged);
consolidateRows(normalizeDepth(safeReadJSON(path_1.default.join(INPUT_DIR, 'ETHUSDT-Depth.json'))), merged);
writeCSV(merged);
